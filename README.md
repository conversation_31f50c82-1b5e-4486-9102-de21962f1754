# AI Novel Generator

AI驱动的小说生成器，支持多模型融合和个性化创作。

## 项目概述

这是一个基于AI技术的小说生成工具，旨在帮助用户创作高质量的小说内容。系统支持多种AI模型，提供灵活的创作控制和个性化定制功能。

## 技术栈

### 后端
- **FastAPI** - 现代、快速的Web框架
- **SQLAlchemy** - ORM数据库操作
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话存储
- **Python 3.12** - 编程语言

### AI模型
- **OpenAI GPT** - 文本生成
- **Anthropic Claude** - 对话和创作
- **Transformers** - 本地模型支持

### 前端
- **React** - 用户界面框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架

## 项目结构

```
ai-novel-generator/
├── backend/                 # 后端服务
│   ├── app/                # 主应用代码
│   │   ├── models/         # 数据模型
│   │   ├── routers/        # API路由
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── config/             # 配置文件
│   ├── requirements.txt    # Python依赖
│   └── main.py            # 应用入口
├── frontend/               # 前端应用
├── database/               # 数据库相关
│   ├── init.sql           # 初始化脚本
│   └── migrations/        # 数据库迁移
├── docs/                   # 项目文档
└── tests/                  # 测试文件
```

## 快速开始

### 环境准备

1. 确保已安装Python 3.12和conda
2. 激活conda环境：
   ```bash
   conda activate fiction
   ```

### 安装依赖

```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt
```

### 配置环境

1. 复制环境配置文件：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件，填入实际的配置值：
   - 数据库连接信息
   - AI模型API密钥
   - 其他必要配置

### 数据库初始化

```bash
# 使用PostgreSQL时，先创建数据库
createdb ai_novel_db

# 运行初始化脚本
psql ai_novel_db < database/init.sql
```

### 启动服务

```bash
# 启动后端服务
cd backend
python main.py
```

服务将在 http://127.0.0.1:8000 启动

### API文档

启动服务后，可以访问：
- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc

## 开发指南

### 代码规范

- 使用Black进行代码格式化
- 使用Flake8进行代码检查
- 使用MyPy进行类型检查

### 测试

```bash
# 运行测试
pytest tests/
```

## 功能特性

- ✅ 项目基础架构
- 🚧 用户认证系统
- 🚧 小说管理功能
- 🚧 AI文本生成
- 🚧 多模型融合
- 🚧 个性化设置

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请创建Issue或联系项目维护者。
