# AI Novel Generator 环境配置示例文件
# 复制此文件为 .env 并填入实际值

# 应用配置
APP_NAME="AI Novel Generator"
APP_VERSION="1.0.0"
DEBUG=false

# 服务器配置
HOST=127.0.0.1
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/ai_novel_db
DATABASE_ECHO=false

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI模型API密钥
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# 文件存储配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
