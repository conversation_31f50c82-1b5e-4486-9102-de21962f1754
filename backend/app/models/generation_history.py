"""
生成历史模型
"""
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.database import Base


class GenerationHistory(Base):
    """生成历史模型"""
    __tablename__ = "generation_history"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    novel_id = Column(String, ForeignKey('novels.id'), nullable=False)
    chapter_id = Column(String, ForeignKey('chapters.id'), nullable=True)
    prompt = Column(Text, nullable=False)
    generated_content = Column(Text)
    model_used = Column(String(50))
    parameters = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    novel = relationship("Novel", back_populates="generation_history")
    chapter = relationship("Chapter", back_populates="generation_history")
    
    def __repr__(self):
        return f"<GenerationHistory(id={self.id}, model_used={self.model_used})>"
