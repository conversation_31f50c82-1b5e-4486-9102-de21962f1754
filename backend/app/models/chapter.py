"""
章节模型
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.database import Base


class Chapter(Base):
    """章节模型"""
    __tablename__ = "chapters"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    novel_id = Column(String, ForeignKey('novels.id'), nullable=False)
    title = Column(String(200), nullable=False)
    content = Column(Text)
    chapter_number = Column(Integer, nullable=False)
    word_count = Column(Integer, default=0)
    status = Column(String(20), default='draft')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    novel = relationship("Novel", back_populates="chapters")
    generation_history = relationship("GenerationHistory", back_populates="chapter", cascade="all, delete-orphan")
    
    # 约束
    __table_args__ = (
        UniqueConstraint('novel_id', 'chapter_number', name='uq_novel_chapter_number'),
    )
    
    def __repr__(self):
        return f"<Chapter(id={self.id}, title={self.title}, chapter_number={self.chapter_number})>"
