"""
角色模型
"""
from sqlalchemy import Column, String, Text, DateTime, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.database import Base


class Character(Base):
    """角色模型"""
    __tablename__ = "characters"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    novel_id = Column(String, ForeignKey('novels.id'), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    personality = Column(Text)
    background = Column(Text)
    role = Column(String(50))  # protagonist, antagonist, supporting, minor
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    novel = relationship("Novel", back_populates="characters")
    
    def __repr__(self):
        return f"<Character(id={self.id}, name={self.name}, role={self.role})>"
