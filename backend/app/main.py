"""
FastAPI应用主文件
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import logging
from loguru import logger

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from config.database import init_db, close_db, check_db_health


# 配置日志
logging.basicConfig(level=getattr(logging, settings.log_level))


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期管理
    """
    # 启动时执行
    logger.info("Starting AI Novel Generator application...")
    
    try:
        # 初始化数据库
        await init_db()
        logger.info("Database initialized successfully")
        
        # 检查数据库健康状态
        if not check_db_health():
            raise Exception("Database health check failed")
            
        logger.info("Application startup completed")
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down AI Novel Generator application...")
    try:
        await close_db()
        logger.info("Application shutdown completed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="AI驱动的小说生成器API",
    lifespan=lifespan,
    debug=settings.debug
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加可信主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "*.localhost"]
)


@app.get("/")
async def root():
    """
    根路径，返回API信息
    """
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """
    健康检查端点
    """
    db_status = check_db_health()
    
    if not db_status:
        raise HTTPException(status_code=503, detail="Database connection failed")
    
    return {
        "status": "healthy",
        "database": "connected",
        "version": settings.app_version
    }


# 注册路由
# TODO: 添加具体的业务路由
# from .routers import auth, novels, characters, generation
# app.include_router(auth.router, prefix="/api/v1/auth", tags=["authentication"])
# app.include_router(novels.router, prefix="/api/v1/novels", tags=["novels"])
# app.include_router(characters.router, prefix="/api/v1/characters", tags=["characters"])
# app.include_router(generation.router, prefix="/api/v1/generation", tags=["generation"])


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
