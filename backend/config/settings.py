"""
应用配置管理
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "AI Novel Generator"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "127.0.0.1"
    port: int = 8000
    
    # 数据库配置
    database_url: str = "postgresql://user:password@localhost:5432/ai_novel_db"
    database_echo: bool = False
    
    # Redis配置（用于缓存和会话）
    redis_url: str = "redis://localhost:6379/0"
    
    # JWT配置
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # AI模型配置
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    
    # 文件存储配置
    upload_dir: str = "uploads"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    
    # CORS配置
    allowed_origins: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    class Config:
        # 找到项目根目录的.env文件
        project_root = Path(__file__).parent.parent.parent
        env_file = project_root / ".env"
        env_file_encoding = "utf-8"


# 创建全局配置实例
settings = Settings()
